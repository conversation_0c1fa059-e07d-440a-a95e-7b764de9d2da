@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }

  /* Amélioration du scroll global */
  html {
    scroll-behavior: smooth;
  }

  /* Styles pour les éléments avec overflow */
  .overflow-auto-custom {
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
  }

  .overflow-hidden-custom {
    overflow: hidden;
  }
}

@layer components {
  .card {
    @apply rounded-xl bg-white shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .input {
    @apply rounded-lg border border-gray-200 px-4 py-2 focus:border-meddoc-primary focus:outline-none focus:ring-2 focus:ring-meddoc-primary/20;
  }

  .button {
    @apply rounded-lg bg-meddoc-primary px-6 py-2 text-white shadow-sm transition-all duration-100 hover:bg-meddoc-primary/90 hover:shadow-md active:scale-95;
  }
}

@layer utilities {
  .animation-delay-500 {
    animation-delay: 500ms;
  }
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  .animation-delay-2000 {
    animation-delay: 2000ms;
  }
}