/* Animations pour les modales */

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes backdropFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes pulseIcon {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* Classes utilitaires pour les animations */
.modal-enter {
  animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-exit {
  animation: modalFadeOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.backdrop-enter {
  animation: backdropFadeIn 0.3s ease-out;
}

.backdrop-exit {
  animation: backdropFadeOut 0.2s ease-in;
}

.pulse-animation {
  animation: pulseIcon 2s infinite;
}

.slide-up-enter {
  animation: slideInUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.slide-up-exit {
  animation: slideOutDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Styles pour les boutons avec effets */
.button-hover-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.button-hover-effect:hover::before {
  left: 100%;
}

/* Effet de glassmorphism pour la modale */
.glass-modal {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass-modal {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(51, 65, 85, 0.3);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
}

/* Animation de chargement pour les boutons */
@keyframes buttonLoading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: buttonLoading 1s linear infinite;
}

/* Effet de focus amélioré */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 
    0 0 0 2px rgba(59, 130, 246, 0.5),
    0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Animation de succès */
@keyframes successCheck {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 0;
  }
}

.success-check {
  animation: successCheck 0.6s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-enter {
    animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .modal-exit {
    animation: slideOutDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
