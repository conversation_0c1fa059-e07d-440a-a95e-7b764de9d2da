/* Scrollbars personnalisées modernes */

/* Wrapper pour le dashboard layout */
.dashboard-layout-wrapper {
  height: 100vh;
  overflow: hidden;
}

/* Scrollbar globale */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.8);
  border-radius: 4px;
  transition: all 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.9);
}

*::-webkit-scrollbar-thumb:active {
  background: rgba(100, 116, 139, 1);
}

/* Scrollbar pour la sidebar */
.MuiDrawer-paper {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.6) transparent;
}

.MuiDrawer-paper::-webkit-scrollbar {
  width: 6px;
}

.MuiDrawer-paper::-webkit-scrollbar-track {
  background: transparent;
}

.MuiDrawer-paper::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.6);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.MuiDrawer-paper::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* Scrollbar pour le contenu principal */
.MuiContainer-root {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.7) rgba(241, 245, 249, 0.5);
}

.MuiContainer-root::-webkit-scrollbar {
  width: 8px;
}

.MuiContainer-root::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 4px;
}

.MuiContainer-root::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.7);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.MuiContainer-root::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.9);
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  * {
    scrollbar-color: rgba(75, 85, 99, 0.8) transparent;
  }

  *::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.8);
  }

  *::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.9);
  }

  *::-webkit-scrollbar-thumb:active {
    background: rgba(55, 65, 81, 1);
  }

  .MuiDrawer-paper {
    scrollbar-color: rgba(75, 85, 99, 0.6) transparent;
  }

  .MuiDrawer-paper::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.6);
  }

  .MuiDrawer-paper::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.8);
  }

  .MuiContainer-root {
    scrollbar-color: rgba(75, 85, 99, 0.7) rgba(31, 41, 55, 0.5);
  }

  .MuiContainer-root::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
  }

  .MuiContainer-root::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.7);
  }

  .MuiContainer-root::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.9);
  }
}

/* Classe utilitaire pour masquer complètement la scrollbar */
.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* Classe utilitaire pour une scrollbar très fine */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.5);
  border-radius: 2px;
}

/* Animation smooth pour le scroll */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Amélioration visuelle pour les éléments avec overflow */
.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
  overflow-x: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
  overflow-y: hidden;
}
