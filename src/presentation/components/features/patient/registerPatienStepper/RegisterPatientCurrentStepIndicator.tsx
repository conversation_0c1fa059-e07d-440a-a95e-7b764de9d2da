import { motion } from "framer-motion";
import { Check } from "lucide-react";

/**
 * Props pour le composant RegisterPatientCurrentStepIndicator
 */
interface RegisterPatientCurrentStepIndicatorProps {
  /** Index de l'étape actuellement active (0-based) */
  activeStep: number;
  steps: string[];
}

/**
 * Indicateur visuel de progression pour le formulaire d'inscription patient
 *
 * Ce composant affiche un stepper horizontal montrant la progression de l'utilisateur
 * à travers les différentes étapes du formulaire d'inscription. Il utilise une
 * animation d'entrée et met en évidence l'étape actuelle avec des styles visuels
 * distincts.
 *
 * @component
 * @example
 * ```tsx
 * <RegisterPatientCurrentStepIndicator activeStep={1} />
 * ```
 *
 * @param {RegisterPatientCurrentStepIndicatorProps} props - Les propriétés du composant
 * @param {number} props.activeStep - L'index de l'étape actuellement active (0-based)
 *
 * @returns {JSX.Element} Stepper horizontal animé avec indicateurs d'étapes
 *
 * @dependencies
 * - steps: Configuration des étapes depuis les constantes
 * - Framer Motion: Pour les animations d'entrée
 *
 */
const RegisterPatientCurrentStepIndicator = ({
  activeStep,
  steps,
}: RegisterPatientCurrentStepIndicatorProps): JSX.Element => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="mb-4 sm:mb-6 lg:mb-6"
    >
      <div className="flex justify-center items-center mb-4 sm:mb-6 px-2 sm:px-4">
        {steps.map((label, index) => (
          <div key={label} className="flex items-center">
            {/* Cercle de l'étape avec label à droite (desktop) */}
            <div className="flex items-center">
              <div
                className={`
                  w-8 h-8 sm:w-9 sm:h-9 lg:w-9 lg:h-9 rounded-full flex items-center justify-center text-xs sm:text-sm lg:text-sm font-bold flex-shrink-0
                  ${
                    index <= activeStep
                      ? "bg-white text-meddoc-primary shadow-lg"
                      : "bg-white/30 text-white border-2 border-white/50"
                  }
                `}
              >
                {index < activeStep ? (
                  <Check size={14} className="sm:w-4 sm:h-4 lg:w-4 lg:h-4" />
                ) : (
                  index + 1
                )}
              </div>

              {/* Label à droite du cercle (desktop style) */}
              <span
                className={`
                  ml-2 sm:ml-3 lg:ml-3 text-xs sm:text-sm lg:text-sm font-medium whitespace-nowrap hidden sm:block
                  ${index <= activeStep ? "text-white" : "text-white/70"}
                `}
              >
                {label}
              </span>
            </div>

            {/* Connecteur entre les étapes */}
            {index < steps.length - 1 && (
              <div
                className={`
                  w-8 sm:w-12 lg:w-12 h-0.5 mx-2 sm:mx-3 lg:mx-3
                  ${index < activeStep ? "bg-white" : "bg-white/40"}
                `}
              />
            )}
          </div>
        ))}
      </div>

      {/* Indicateur textuel de l'étape actuelle pour mobile */}
      <div className="block sm:hidden text-center">
        <span className="text-white/90 text-sm font-medium">
          Étape {activeStep + 1} : {steps[activeStep]}
        </span>
      </div>
    </motion.div>
  );
};

export default RegisterPatientCurrentStepIndicator;
