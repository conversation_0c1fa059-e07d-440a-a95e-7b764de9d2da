import React from 'react';

const ScrollbarTest = () => {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Test des Scrollbars Personnalisées</h2>
      
      {/* Test scrollbar verticale */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-2">Scrollbar Verticale</h3>
        <div 
          className="h-32 overflow-y-auto border border-gray-200 rounded p-2"
          style={{ maxHeight: '128px' }}
        >
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} className="py-1 border-b border-gray-100 last:border-b-0">
              Ligne de contenu {i + 1} - Lorem ipsum dolor sit amet consectetur
            </div>
          ))}
        </div>
      </div>

      {/* Test scrollbar horizontale */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-2">Scrollbar Horizontale</h3>
        <div 
          className="w-full overflow-x-auto border border-gray-200 rounded p-2"
          style={{ maxWidth: '100%' }}
        >
          <div className="flex space-x-4" style={{ minWidth: '800px' }}>
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="flex-shrink-0 w-32 h-16 bg-blue-100 rounded flex items-center justify-center">
                Item {i + 1}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Test scrollbar fine */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-2">Scrollbar Fine</h3>
        <div 
          className="h-24 overflow-y-auto border border-gray-200 rounded p-2 scrollbar-thin"
          style={{ maxHeight: '96px' }}
        >
          {Array.from({ length: 15 }, (_, i) => (
            <div key={i} className="py-1 text-sm">
              Contenu avec scrollbar fine {i + 1}
            </div>
          ))}
        </div>
      </div>

      {/* Test scrollbar masquée */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-2">Scrollbar Masquée</h3>
        <div 
          className="h-24 overflow-y-auto border border-gray-200 rounded p-2 scrollbar-hidden"
          style={{ maxHeight: '96px' }}
        >
          {Array.from({ length: 15 }, (_, i) => (
            <div key={i} className="py-1 text-sm">
              Contenu avec scrollbar masquée {i + 1}
            </div>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-1">
          La scrollbar est masquée mais le contenu reste scrollable
        </p>
      </div>
    </div>
  );
};

export default ScrollbarTest;
