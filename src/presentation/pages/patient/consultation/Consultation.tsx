import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { motion } from "framer-motion";
import { FormStepperWrapper } from "@/presentation/components/common/ScrollToTop";
import RegisterPatientCurrentStepIndicator from "@/presentation/components/features/patient/registerPatienStepper/RegisterPatientCurrentStepIndicator";
import ConsultationForm from "./ConsultationForm";
import { useConsultationStepLogic } from "@/presentation/hooks/consultationStep/useConsultationStepLogic";
import ConsultationInformations from "./ConsultationInformations";
import { CONSULTATION_STEPS } from "@/shared/constants/ConsultationtSteps";
import { useLocation } from "react-router-dom";
import useProfessionals from "@/presentation/hooks/use-professionals";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { useEffect } from "react";
import useGetAuthenticatedUser from "@/presentation/hooks/user/use-get-authenticated-user";
import { getSelectedTimeSlot } from "@/presentation/hooks/consultationStep/getSelectedTimeSlot";
import { categorie_enum } from "@/domain/models/enums/categorieEnum.ts";

const Consultation = (): JSX.Element => {
  const {
    activeStep,
    control,
    errors,
    isLoading,
    getValues,
    register,
    setValue,
    watch,
    handleSubmit,
    handleNextStep,
    handlePreviousStep,
    onSubmit,
  } = useConsultationStepLogic();
  const { loading, getProfessionalsById } = useProfessionals();
  const selectedTimeSlot = getSelectedTimeSlot();
  const { id: patient_id } = useGetAuthenticatedUser();

  useEffect(() => {
    if (selectedTimeSlot && selectedTimeSlot.id_professionnel) {
      getProfessionalsById(selectedTimeSlot.id_professionnel);
    }
    if (selectedTimeSlot && selectedTimeSlot?.isRegister) {
      setValue(
        "categorie",
        selectedTimeSlot.step1Data.typeDeConsultation as categorie_enum
      );
      setValue("speciality", selectedTimeSlot.step1Data.specialiteMedicale);
      setValue(
        "consultationMotif",
        selectedTimeSlot.step1Data.motifDeConsultation
      );
      handleNextStep(4);
    }
    // handleresetState();
  }, []);

  if (loading) {
    return (
      <div className="flex flex-col gap-4 text-center justify-center items-center min-h-[100vh]">
        <LoadingSpinner className="h-auto" />
      </div>
    );
  } else {
    return (
      <UnathenticatedLayout>
        <FormStepperWrapper
          activeStep={activeStep}
          config={{
            behavior: "smooth",
            delay: 200,
            top: 0,
          }}
        >
          {/* Fond dégradé pour toute la page */}
          <div className="min-h-screen bg-gradient-to-br from-blue-400 via-teal-400 to-green-400 py-12 md:px-4">
            <div className="md:container md:mx-auto max-w-6xl">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-8"
              >
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 text-center">
                  Prenez votre rendez-vous en ligne
                </h1>
                <p className="text-white/90 text-base sm:text-lg text-center">
                  Renseignez les informations suivantes
                </p>
              </motion.div>
              {/* Stepper moderne */}
              <RegisterPatientCurrentStepIndicator
                steps={CONSULTATION_STEPS}
                activeStep={activeStep}
              />

              {/* Carte principale avec design contact */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white rounded-3xl shadow-2xl overflow-hidden"
              >
                <div className="flex flex-col lg:flex-row min-h-[600px]">
                  {/* Section gauche - Information */}
                  <ConsultationInformations
                    activeStep={activeStep}
                    getValues={getValues}
                  />

                  {/* Section droite - Formulaire */}
                  <ConsultationForm
                    onNext={handleNextStep}
                    onBack={handlePreviousStep}
                    activeStep={activeStep}
                    control={control}
                    errors={errors}
                    isDisabled={isLoading}
                    isLoading={isLoading}
                    register={register}
                    setValue={setValue}
                    watch={watch}
                    onSubmit={() => onSubmit(patient_id)}
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </FormStepperWrapper>
      </UnathenticatedLayout>
    );
  }
};

export default Consultation;
