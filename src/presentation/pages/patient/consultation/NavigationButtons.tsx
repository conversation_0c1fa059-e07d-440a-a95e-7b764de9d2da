import { memo } from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface NavigationButtonsProps {
  activeStep: number;
  onBack: (count: number) => void;
  onNext: (count: number) => void;
  isDisabled: boolean;
}

export const NavigationButtons = memo(
  ({ activeStep, onBack, onNext, isDisabled }: NavigationButtonsProps) => {
    const handleNext = () => {
      onNext(1);
    };
    const handleBack = () => {
      if (activeStep === 3) {
        onBack(3);
      } else {
        onBack(1);
      }
    };

    return (
      <div className="flex justify-between items-center gap-3 mt-6 px-4">
        {/* Bouton Retour */}
        <motion.button
          onClick={handleBack}
          disabled={activeStep === 0}
          className={`
            flex items-center gap-1.5 px-4 py-2 rounded-lg font-medium text-sm
            transition-all duration-300 ease-out
            ${
              activeStep === 0
                ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-md border border-gray-200"
            }
          `}
          whileHover={activeStep !== 0 ? { scale: 1.02 } : {}}
          whileTap={activeStep !== 0 ? { scale: 0.98 } : {}}
        >
          <ChevronLeft size={16} />
          <span>Retour</span>
        </motion.button>

        {/* Bouton Suivant */}
        {activeStep != 1 && activeStep != 3 && (
          <motion.button
            onClick={handleNext}
            disabled={isDisabled}
            className={`
              flex items-center gap-1.5 px-4 py-2 rounded-lg font-medium text-sm
              transition-all duration-300 ease-out
              ${
                isDisabled
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white hover:from-meddoc-primary/90 hover:to-meddoc-secondary/90 hover:shadow-md"
              }
            `}
            whileHover={!isDisabled ? { scale: 1.02 } : {}}
            whileTap={!isDisabled ? { scale: 0.98 } : {}}
          >
            <span>Suivant</span>
            <ChevronRight size={16} />
          </motion.button>
        )}
      </div>
    );
  }
);

NavigationButtons.displayName = "NavigationButtons";
